import { Locale, siteConfig } from '@/config/global'
import {
  Button,
  Dropdown,
  Label,
  Navbar,
  TextInput,
  NavbarBrand,
  NavbarToggle,
  NavbarCollapse,
  NavbarLink,
  DropdownItem,
  Tooltip,
} from 'flowbite-react'
import { HiSearch } from 'react-icons/hi'
import { FaDiscord, FaTelegram, FaDesktop } from 'react-icons/fa'
import { Globe } from 'lucide-react'
import { Languages } from './client/languages'
import { NavbarLinks } from './client/navbar-links'
import { LANGUAGES } from '@/config/global'
import { getTranslations, getLocale } from 'next-intl/server'
import { Link } from '@/navigation'

export async function SiteHeader() {
  const locale = await getLocale()
  const t = await getTranslations('header')

  return (
    <header className="sticky top-0 z-40 border-b border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900">
      <Navbar>
        <NavbarBrand href="/">
          {/* <img
            src="/images/logo.svg"
            className="mr-3 h-6 sm:h-9"
            alt="Logo"
          /> */}
          <span className="self-center whitespace-nowrap text-xl font-semibold dark:text-white">{siteConfig.name}</span>
        </NavbarBrand>

        <div className="flex items-center lg:order-2">
          {/* <form className="hidden md:block">
            <Label htmlFor="search-bar" className="sr-only">
              Search
            </Label>
            <TextInput
              icon={HiSearch}
              id="search-bar"
              placeholder="Search"
              type="search"
              className="mr-2 block [&_input]:py-2"
            />
          </form> */}
          {/* <span className="pr-2">
            <NavbarToggle
              barIcon={HiSearch}
              theme={{
                icon: "h-5 w-5 text-gray-900 dark:text-gray-400 md:hidden",
              }}
            />
          </span> */}

          <div className="hidden items-center gap-2 sm:flex">
            <a
              href={siteConfig.socials.telegram}
              target="_blank"
              rel="nofollow"
              className="inline-flex items-center rounded-lg p-2 text-sm font-medium text-gray-500 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-600"
            >
              <Tooltip content={t('joinTelegramGroup')}>
                <FaTelegram className="h-5 w-5" />
              </Tooltip>
            </a>
            <a
              href={siteConfig.socials.discord}
              target="_blank"
              rel="nofollow"
              className="inline-flex items-center rounded-lg p-2 text-sm font-medium text-gray-500 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-600"
            >
              <Tooltip content={t('joinDiscordServer')}>
                <FaDiscord className="h-5 w-5" />
              </Tooltip>
            </a>
            <Link
              href="/app"
              prefetch={false}
              className="inline-flex items-center rounded-lg p-2 text-sm font-medium text-gray-500 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-600"
            >
              <Tooltip content={t('downloadApp')}>
                <FaDesktop className="h-5 w-5" />
              </Tooltip>
            </Link>
          </div>

          <span className="mx-3 hidden h-5 w-px bg-gray-200 dark:bg-gray-600 lg:ml-3 lg:inline" />
          <Dropdown
            color="gray"
            label={
              <span className="flex items-center px-0 dark:text-gray-300">
                <Globe className="me-2 h-4 w-4 text-gray-500" />
                {LANGUAGES[locale as Locale]}
              </span>
            }
            theme={{
              floating: {
                target:
                  'w-fit border-0 ring-0 [&_span]:px-2 [&_span]:text-gray-900 [&_span]:hover:text-gray-900 [&_span]:dark:text-gray-300 [&_span]:dark:hover:text-gray-300',
              },
            }}
          >
            <Languages />
          </Dropdown>
          <NavbarToggle theme={{ icon: 'h-5 w-5 shrink-0' }} className="ml-1" />
        </div>

        <NavbarCollapse
          theme={{
            list: 'mt-4 flex flex-col lg:mt-0 lg:flex-row lg:text-base lg:space-x-4 xl:space-x-8 lg:font-medium',
          }}
          className="lg:order-1"
        >
          {/* <form className="mb-3 flex w-full items-center gap-3 md:hidden">
            <div className="flex-1">
              <Label htmlFor="search-bar" className="sr-only">
                Search
              </Label>
              <TextInput
                icon={HiSearch}
                id="search-bar"
                placeholder="Search for anything..."
                type="search"
                className="py-1.5"
              />
            </div>
            <Button type="submit">
              <HiSearch className="mr-2 h-5 w-5 text-gray-100" />
              Search
            </Button>
          </form> */}
          <NavbarLinks />
        </NavbarCollapse>
      </Navbar>
    </header>
  )
}
