import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { siteConfig } from '@/config/global'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

const INTERNAL_HOSTNAMES = [new URL(siteConfig.url).hostname, 'iiilab.com']
export function isInternalUrl(url: string): boolean {
  try {
    const hostname = new URL(url).hostname
    return INTERNAL_HOSTNAMES.includes(hostname)
  } catch (e) {
    // TypeError: Invalid URL  e.g. relative url
    return true
  }
}
