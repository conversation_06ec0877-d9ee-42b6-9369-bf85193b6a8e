export type PartialRecord<K extends keyof any, T> = Partial<Record<K, T>>
import { type BlocksContent } from '@strapi/blocks-react-renderer'

export interface MediaFormat {
  quality: number
  video_url: string
  video_ext: string | null
  video_size: number | null
  audio_url: string | null
  audio_ext: string | null
  audio_size: number | null
  separate: number
  quality_note: string
  alternate_url?: string
  language?: string
}

export interface Media {
  media_type: string
  resource_url: string
  preview_url: string | null
  formats?: MediaFormat[]
}

export interface ExtractResult {
  text: string | null
  medias: Media[]
}

export interface FailureResult {
  message: string
  code?: string
}

export interface ArticleMeta {
  title: string
  slug: string
  description: string
}

export interface Article extends ArticleMeta {
  category: string
  content: BlocksContent
  updatedAt: Date
  publishedAt: Date
}

export interface PaginationInfo {
  page: number
  pageSize: number
  pageCount: number
  total: number
}

export type DeviceType = 'iOS' | 'Mobile' | 'Desktop'
