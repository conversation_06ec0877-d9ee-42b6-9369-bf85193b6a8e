'use client'

import { ArrowDownToLine } from 'lucide-react'
import { Loader2, Loader, XCircle, VolumeX } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { KeyboardEvent, useState, useRef, useEffect } from 'react'
import { getDomainFromUrl, getSubDomainFromUrl, getUrlFromText } from '@gongyinshi/common/url'
import { useParams, useSearchParams } from 'next/navigation'
import { SiteID, domainSiteMap, iiiLabDomainSiteMap } from '@/config/downloader'
import { G_FOOTER, G_TIMESTAMP, ACCEPT_LANGUAGE } from '@gongyinshi/common/request'
import { Locale, SIGNATURE_KEY } from '@/config/global'
import md5 from 'md5'
import { useRouter } from '@/navigation'
import { usePathname } from 'next/navigation'
import { siteConfig } from '@/config/global'
import { ExtractResult, Media, MediaFormat, FailureResult } from '@/types/global'
import { isIOS, isMobile, isDesktop } from 'react-device-detect'
import { DeviceType } from '@/types/global'
import * as Sentry from '@sentry/nextjs'
import { AdBanner } from './ad-banner'
import { TextInput, Button, Alert, Card } from 'flowbite-react'
import { HiLink, HiInformationCircle } from 'react-icons/hi'
import { FaPaste } from 'react-icons/fa'
import { TbChevronDown } from 'react-icons/tb'
import { CiVideoOn, CiImageOn, CiMusicNote1 } from 'react-icons/ci'
import { API_BASE_URL } from '@/config/global'

export function Extractor({ placeholder, actionButtonText }: { placeholder?: string; actionButtonText?: string }) {
  const params = useParams<{ locale: Locale; site: SiteID }>()
  const t = useTranslations('extractor.client')
  const router = useRouter()
  const searchParams = useSearchParams()
  const fullPathname = usePathname()

  const [loading, setLoading] = useState(false)
  const [text, setText] = useState('')
  const [errorMsg, setErrorMsg] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const [result, setResult] = useState<ExtractResult | null>(null)
  const [readClipboardSupported, setReadClipboardSupported] = useState(false)
  const [deviceType, setDeviceType] = useState<DeviceType>()
  const [sponsor, setSponsor] = useState(false)
  const [mediaInfo, setMediaInfo] = useState<Media | null>(null)

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.code === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleSubmit = async (link?: string) => {
    link = link ?? text
    if (!link) {
      setErrorMsg(t('noInput'))
      inputRef.current?.focus()
      return
    }

    // parse url from text
    const url = getUrlFromText(link)
    const domain = getDomainFromUrl(url)
    if (!url || !domain) {
      setErrorMsg(t('wrongLink'))
      inputRef.current?.focus()
      return
    }

    // display pure url
    setText(url)

    const subDomain = getSubDomainFromUrl(url)
    // redirect to matched site downloader page
    const matchedSiteID = domainSiteMap[subDomain] || domainSiteMap[domain]
    if (matchedSiteID && matchedSiteID !== params.site) {
      router.push(`/${matchedSiteID}?link=${encodeURI(url)}`)
      return
    }

    // redirect to iiiLab
    const matchedLabSiteID = iiiLabDomainSiteMap[subDomain] || iiiLabDomainSiteMap[domain]
    if (matchedLabSiteID) {
      location.href = `https://${matchedLabSiteID}.iiilab.com?link=${encodeURI(url)}`
      return
    }

    // extract
    setResult(null)
    setMediaInfo(null)
    setErrorMsg('')
    setSponsor(false)
    setLoading(true)
    const timestamp = Date.now()
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }
    headers[ACCEPT_LANGUAGE] = params.locale
    headers[G_TIMESTAMP] = String(timestamp)
    headers[G_FOOTER] = md5(url + params.locale + timestamp + SIGNATURE_KEY)
    try {
      const res = await fetch(`${API_BASE_URL}/v1/extract`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          link: url,
        }),
      })
      if (res.ok) {
        const data: ExtractResult = await res.json()
        setResult(data)
        if (Number(data.medias[0]?.formats?.length || 0) > 1) {
          setMediaInfo(data.medias[0])
        }
      } else {
        const data: FailureResult = await res.json()
        // Display Sponsors
        if (data.code === 'ShowSponsorAds') {
          setSponsor(true)
        }
        setErrorMsg(data.message)
      }
    } catch (e) {
      if (e instanceof TypeError) {
        // Network Error
        setErrorMsg(t('networkError'))
      } else {
        setErrorMsg(t('serverError')) // Other Error
        Sentry.captureException(e)
      }
    } finally {
      setLoading(false)
    }
  }

  const pasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text) {
        setText(text)
      }
    } catch (err) {
      console.error('Failed to read clipboard contents.', err)
    }
  }

  const handleMediaInfoClick = (media: Media) => {
    setMediaInfo(media)
  }

  // page loaded
  useEffect(() => {
    const link = searchParams.get('link')
    if (link) {
      setText(link)
      handleSubmit(link) // React waits until all code in the event handlers has run before processing your state updates
      history.replaceState(null, '', fullPathname)
    }
    if (navigator.clipboard && navigator.clipboard.readText && typeof navigator.clipboard.readText === 'function') {
      setReadClipboardSupported(true)
    }
    // detect device type
    if (isIOS) {
      setDeviceType('iOS')
    } else if (isMobile) {
      setDeviceType('Mobile')
    } else if (isDesktop) {
      setDeviceType('Desktop')
    }
  }, []) // This runs only on mount (when the component appears)

  return (
    <>
      <div className="flex w-full max-w-3xl pt-8 sm:flex-row flex-col gap-y-4">
        <div className="relative w-full">
          <TextInput
            icon={() => <HiLink className="h-5 w-5 text-gray-500 dark:text-gray-400" />}
            value={text}
            onChange={(e) => setText(e.target.value.trim())}
            onKeyDown={handleKeyDown}
            ref={inputRef}
            placeholder={placeholder ?? t('placeholder')}
            type="text"
            className="w-full sm:[&_input]:rounded-r-none [&_input]:py-3 [&_input]:pe-10"
          />
          {text && <XCircle className="absolute h-6 w-6 end-3 top-3 cursor-pointer" onClick={() => setText('')} />}
          {!text && readClipboardSupported && (
            <FaPaste className="absolute h-6 w-6 end-3 top-3 cursor-pointer" onClick={pasteFromClipboard} />
          )}
        </div>
        <Button className="sm:rounded-l-none [&>span]:px-5" disabled={loading} onClick={() => handleSubmit()}>
          {loading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : <ArrowDownToLine className="mr-2 h-5 w-5" />}
          <span className="whitespace-nowrap">{actionButtonText ?? t('download')}</span>
        </Button>
      </div>

      {loading && (
        <div className="py-12">
          <Loader className="h-10 w-10 animate-spin" />
        </div>
      )}

      {errorMsg && (
        <Alert
          color="failure"
          className="w-full max-w-3xl mt-4"
          icon={HiInformationCircle}
          additionalContent={sponsor && <Sponsors />}
        >
          {errorMsg}
        </Alert>
      )}

      {result && (
        <Card className="mt-2 w-full max-w-3xl flex flex-col gap-8">
          <div className="text-center text-sm font-medium">{result.text}</div>
          <div className="flex items-center gap-2 justify-center flex-wrap">
            {result.medias.map((media, index) => (
              <DownloadButton media={media} key={index} onMediaInfoClick={handleMediaInfoClick} />
            ))}
          </div>

          {mediaInfo && (
            <div
              className={`border border-dashed rounded-lg px-8 py-4 ${
                mediaInfo.media_type === 'audio' ? 'bg-purple-100' : 'bg-blue-50'
              }`}
            >
              {mediaInfo.media_type === 'audio' ? (
                <p className="text-sm text-center mb-4 text-purple-700">{t('moreAudioOptions')}</p>
              ) : (
                <p className="text-sm text-center mb-4 text-blue-700">{t('moreQualityOptions')}</p>
              )}

              <div className="flex flex-wrap items-center gap-4">
                {mediaInfo.formats?.map((format, index) => (
                  <FormatDownloadButton format={format} key={index}>
                    <>
                      <ArrowDownToLine className="mr-[2px] w-[14px] h-[14px] flex-shrink-0" />
                      <span>{!!format.video_url ? `${format.quality}P（${format.video_ext}）` : format.language}</span>
                    </>
                  </FormatDownloadButton>
                ))}
              </div>

              <div className="w-full flex flex-col items-center mt-4">
                <p className="text-sm text-gray-500">{t('resolutionDownloadTip')}</p>
              </div>
            </div>
          )}

          {deviceType && (
            <p className="text-sm text-center text-gray-500 pt-2">
              {t('downloadTip')}
              {deviceType === 'iOS' && t('iosTip')}
              {deviceType === 'Mobile' && t('mobileTip')}
              {deviceType === 'Desktop' && t('desktopTip')}
            </p>
          )}
        </Card>
      )}

      <AdBanner />
    </>
  )
}

function Sponsors() {
  const t = useTranslations('extractor.client')
  return (
    <div className="text-black bg-slate-50 border border-dashed p-4 mt-2 rounded-lg">
      <h4 className="text-base">
        {t('recommendedProProducts')}&nbsp;<span className="text-sm">({t('sponsors')})</span>
      </h4>
      <div className="flex flex-col md:flex-row justify-center gap-2 items-center mt-4">
        <a
          href="https://www.henghengmao.com?from=snapany"
          target="_blank"
          className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-gray-700 rounded-lg bg-gray-200 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <img src="/images/hhm-128.png" className="w-10 h-10 me-3 rounded-md" alt="亨亨猫批量去水印" />
          <div className="w-full flex-col">
            <p>亨亨猫批量去水印</p>
            <p className="text-xs">电脑版软件功能非常强大</p>
          </div>
        </a>

        <a
          href="https://landing.shuiyinyu.com/pages/dashen?from=snapany"
          target="_blank"
          className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-gray-700 rounded-lg bg-gray-200 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <img src="/images/dashen-100.png" className="w-10 h-10 me-3 rounded-md" alt="大神水印APP" />
          <div className="w-full flex-col">
            <p>大神水印APP</p>
            <p className="text-xs">支持苹果iOS/安卓手机</p>
          </div>
        </a>

        <a
          href="https://app.xibuzhineng.com?from=snapany"
          target="_blank"
          className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-gray-700 rounded-lg bg-gray-200 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white"
        >
          <img src="/images/xzw-100.png" className="w-10 h-10 me-3 rounded-md" alt="下载王安卓APP" />
          <div className="w-full flex-col">
            <p>下载王安卓APP</p>
            <p className="text-xs">手机上也能批量下载</p>
          </div>
        </a>
      </div>
    </div>
  )
}

function FormatDownloadButton({ format, children }: { format: MediaFormat; children: React.ReactNode }) {
  const t = useTranslations('extractor.client')
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const isVideo = !!format.video_url

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div
      className={`relative h-[40px] flex items-center justify-between rounded-lg text-white cursor-pointer box-border ${
        isVideo ? 'bg-blue-700' : 'bg-purple-700'
      }`}
      ref={dropdownRef}
    >
      <a
        href={isVideo ? format.video_url : (format.audio_url as string)}
        target="_blank"
        rel="noreferrer"
        download={`${siteConfig.name}.${isVideo ? format.video_ext : format.audio_ext}`}
        className="flex items-center justify-center w-full p-[10px] text-sm"
      >
        {children}
      </a>
      {format.alternate_url && (
        <div
          className={`px-[10px] bg-white h-full rounded-r-lg font-bold text-sm flex items-center justify-center cursor-pointer border ${
            isVideo ? 'text-blue-700 border-blue-700' : 'text-purple-700 border-purple-700'
          }`}
          onClick={() => setShowDropdown(!showDropdown)}
        >
          <TbChevronDown className="stroke-[3]" />
        </div>
      )}
      {showDropdown && (
        <div
          className="absolute top-full w-full bg-white rounded shadow-md z-50 text-sm text-gray-500 text-center"
          onClick={(e) => {
            e.stopPropagation()
            setShowDropdown(false)
          }}
        >
          <a
            href={format.alternate_url}
            target="_blank"
            rel="noreferrer"
            download={`${siteConfig.name}.${isVideo ? format.video_ext : format.audio_ext}`}
            className="block p-[10px]"
          >
            {t('alternativeLink')}
          </a>
        </div>
      )}
    </div>
  )
}

function DownloadButton({ media, onMediaInfoClick }: { media: Media; onMediaInfoClick: (media: Media) => void }) {
  const t = useTranslations('extractor.client')
  switch (media.media_type) {
    case 'image':
      return (
        <Button
          as="a"
          color="gray"
          href={media.resource_url}
          target="_blank"
          rel="noreferrer"
          download={`${siteConfig.name}.jpg`}
        >
          <CiImageOn className="me-2 h-5 w-5" />
          {t('downloadImage')}
        </Button>
      )
    case 'audio':
      return (
        <>
          {media.formats ? (
            media.formats.length > 1 ? (
              <Button color="purple" onClick={() => onMediaInfoClick(media)}>
                <CiMusicNote1 className="me-2 h-5 w-5" />
                {t('downloadAudio')}
              </Button>
            ) : (
              <FormatDownloadButton format={media.formats[0]}>
                <>
                  <CiMusicNote1 className="me-2 h-5 w-5" />
                  {t('downloadAudio')}
                </>
              </FormatDownloadButton>
            )
          ) : (
            <Button
              as="a"
              color="purple"
              href={media.resource_url}
              target="_blank"
              rel="noreferrer"
              download={`${siteConfig.name}.mp3`}
            >
              <CiMusicNote1 className="me-2 h-5 w-5" />
              {t('downloadAudio')}
            </Button>
          )}
          {media.preview_url && (
            <Button
              as="a"
              color="gray"
              href={media.preview_url}
              target="_blank"
              rel="noreferrer"
              download={`${siteConfig.name}.jpg`}
            >
              <CiImageOn className="me-2 h-5 w-5" />
              {t('downloadThumbnail')}
            </Button>
          )}
        </>
      )
    default:
      return (
        <>
          {media.formats ? (
            media.formats.length > 1 ? (
              <Button onClick={() => onMediaInfoClick(media)}>
                <CiVideoOn className="me-2 h-5 w-5" />
                {t('downloadVideo')}
              </Button>
            ) : (
              <FormatDownloadButton format={media.formats[0]}>
                <>
                  <CiVideoOn className="me-2 h-5 w-5" />
                  {t('downloadVideo')}
                </>
              </FormatDownloadButton>
            )
          ) : (
            <Button
              as="a"
              href={media.resource_url}
              target="_blank"
              rel="noreferrer"
              download={`${siteConfig.name}.mp4`}
            >
              <CiVideoOn className="me-2 h-5 w-5" />
              {t('downloadVideo')}
            </Button>
          )}
          {media.preview_url && (
            <Button
              as="a"
              color="gray"
              href={media.preview_url}
              target="_blank"
              rel="noreferrer"
              download={`${siteConfig.name}.jpg`}
            >
              <CiImageOn className="me-2 h-5 w-5" />
              {t('downloadThumbnail')}
            </Button>
          )}
        </>
      )
  }
}
