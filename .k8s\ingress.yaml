apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: snapany-web
  annotations:
    nginx.ingress.kubernetes.io/from-to-www-redirect: 'true'
spec:
  ingressClassName: nginx
  rules:
    - host: snapany.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: snapany-web
                port:
                  number: 80
