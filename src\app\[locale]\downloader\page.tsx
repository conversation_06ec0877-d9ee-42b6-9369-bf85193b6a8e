import { notFound } from 'next/navigation'
import { Locale } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { useTranslations, useMessages, NextIntlClientProvider } from 'next-intl'
import { Video, Download, Zap } from 'lucide-react'
import { pick } from 'lodash'
import { Accordion, AccordionContent, AccordionPanel, AccordionTitle } from 'flowbite-react'
import { siteConfig } from '@/config/global'
import { FAQ } from '@/config/downloader'

// 页面元数据配置
import DownloadManager from '@/components/client/download-manager'
const METADATA: Record<Locale, { title: string; description: string }> = {
  en: {
    title: 'SnapAny Video Download Extension',
    description: 'Install the extension to quickly download videos from any site. Easy to use, safe, and completely free—forever.'
  },
  zh: {
    title: 'SnapAny 视频下载插件',
    description: '安装 SnapAny 插件，轻松保存网页视频。操作便捷，安全可靠，永久免费使用。'
  },
  ja: {
    title: 'SnapAny ビデオダウンロード拡張機能',
    description: '拡張機能をインストールして、あらゆるサイトから動画を素早くダウンロード。使いやすく、安全で、完全に無料—永続的に。'
  },
  es: {
    title: 'SnapAny Extensión de Descarga de Videos',
    description: 'Instala la extensión para descargar rápidamente videos de cualquier sitio. Fácil de usar, seguro y completamente gratis—para siempre.'
  }
}

// FAQ数据配置
const FAQs: Record<Locale, FAQ[]> = {
  en: [
    {
      question: 'Can I close the download tab while it\'s running?',
      answer: 'The download tab must stay open for the process to complete. Closing it prematurely may interrupt the download.'
    },
    {
      question: 'Why can\'t SnapAny detect videos on certain websites?',
      answer: 'Some websites use custom encryption or player structures that may block detection. Try alternative pages or use the recording mode as a fallback.'
    },
    {
      question: 'Why is the download speed inconsistent?',
      answer: 'Speed depends on your internet connection, source server, and browser capacity. For optimal results, avoid downloading multiple large files simultaneously.'
    },
    {
      question: 'Can I download multiple videos at once?',
      answer: 'SnapAny supports multi-task downloads. However, downloading several HD videos at once may strain your system. Use this feature based on your device capacity.'
    },
    {
      question: 'What should I do if the downloaded video won\'t play?',
      answer: 'Some videos use special codecs (like H.265) that aren\'t supported by all players. We recommend using VLC Player for best compatibility.'
    },
    {
      question: 'Why is the download missing audio or video?',
      answer: 'Some media use separate audio and video streams. Use recording mode or merge the tracks with third-party tools.'
    },
    {
      question: 'Where are my downloaded files stored?',
      answer: 'Files are saved in your browser\'s default download folder. If you can\'t find them, check your download history or browser settings.'
    }
  ],
  zh: [
    {
      question: '下载页面可以关闭吗？',
      answer: '下载任务依赖打开的标签页进行处理，请在文件完全保存前保持下载页处于激活状态。'
    },
    {
      question: '为什么 SnapAny 无法识别某些网页上的视频？',
      answer: '部分网站采用了特殊的加密或自定义播放技术，可能会导致资源嗅探失败。我们建议尝试其他页面或使用屏幕录制功能。'
    },
    {
      question: '为什么下载速度不稳定？',
      answer: '下载速度可能受到您当前的网络环境、视频源服务器限制以及浏览器性能影响。建议在网络稳定时进行下载，避免同时下载多个大型文件。'
    },
    {
      question: '可以同时下载多个视频文件吗？',
      answer: 'SnapAny 支持多任务下载，但请注意：同时下载多个高清视频可能会占用较多内存和带宽，建议根据设备性能适度使用。'
    },
    {
      question: '下载的视频无法播放怎么办？',
      answer: '有些视频采用特定编码格式（如 H.265），部分播放器可能不支持。建议使用兼容性更强的播放器（如 VLC）进行播放。'
    },
    {
      question: '下载后只有声音或只有画面？',
      answer: '部分媒体内容采用分离加载方式，音视频分别传输。建议开启录制模式或使用工具合并音视频轨道。'
    },
    {
      question: '文件保存在哪里了？',
      answer: '所有文件会保存在浏览器的默认下载文件夹中。若找不到，请检查您的下载历史或浏览器设置。'
    }
  ],
  ja: [
    {
      question: 'ダウンロード中にタブを閉じても大丈夫ですか？',
      answer: 'ダウンロードプロセスを完了するには、タブを開いたままにしておく必要があります。途中で閉じるとダウンロードが中断される可能性があります。'
    },
    {
      question: 'なぜSnapAnyは特定のウェブサイトで動画を検出できないのですか？',
      answer: '一部のウェブサイトでは、カスタム暗号化やプレーヤー構造を使用しており、検出をブロックする場合があります。代替ページを試すか、録画モードをフォールバックとして使用してください。'
    },
    {
      question: 'なぜダウンロード速度が不安定なのですか？',
      answer: '速度は、インターネット接続、ソースサーバー、ブラウザの容量に依存します。最適な結果を得るには、複数の大きなファイルを同時にダウンロードすることを避けてください。'
    },
    {
      question: '複数の動画を同時にダウンロードできますか？',
      answer: 'SnapAnyはマルチタスクダウンロードをサポートしています。ただし、複数のHD動画を同時にダウンロードすると、システムに負荷がかかる場合があります。デバイスの容量に基づいてこの機能を使用してください。'
    },
    {
      question: 'ダウンロードした動画が再生されない場合はどうすればよいですか？',
      answer: '一部の動画では、すべてのプレーヤーでサポートされていない特別なコーデック（H.265など）を使用しています。最高の互換性を得るには、VLCプレーヤーの使用をお勧めします。'
    },
    {
      question: 'なぜダウンロードで音声または動画が欠けているのですか？',
      answer: '一部のメディアでは、別々の音声および動画ストリームを使用しています。録画モードを使用するか、サードパーティツールでトラックをマージしてください。'
    },
    {
      question: 'ダウンロードしたファイルはどこに保存されますか？',
      answer: 'ファイルは、ブラウザのデフォルトのダウンロードフォルダに保存されます。見つからない場合は、ダウンロード履歴またはブラウザ設定を確認してください。'
    }
  ],
  es: [
    {
      question: '¿Puedo cerrar la pestaña de descarga mientras se está ejecutando?',
      answer: 'La pestaña de descarga debe permanecer abierta para que el proceso se complete. Cerrarla prematuramente puede interrumpir la descarga.'
    },
    {
      question: '¿Por qué SnapAny no puede detectar videos en ciertos sitios web?',
      answer: 'Algunos sitios web utilizan cifrado personalizado o estructuras de reproductor que pueden bloquear la detección. Prueba páginas alternativas o usa el modo de grabación como respaldo.'
    },
    {
      question: '¿Por qué la velocidad de descarga es inconsistente?',
      answer: 'La velocidad depende de tu conexión a internet, servidor de origen y capacidad del navegador. Para obtener resultados óptimos, evita descargar múltiples archivos grandes simultáneamente.'
    },
    {
      question: '¿Puedo descargar múltiples videos a la vez?',
      answer: 'SnapAny admite descargas multitarea. Sin embargo, descargar varios videos HD a la vez puede sobrecargar tu sistema. Usa esta función según la capacidad de tu dispositivo.'
    },
    {
      question: '¿Qué debo hacer si el video descargado no se reproduce?',
      answer: 'Algunos videos usan códecs especiales (como H.265) que no son compatibles con todos los reproductores. Recomendamos usar VLC Player para la mejor compatibilidad.'
    },
    {
      question: '¿Por qué falta audio o video en la descarga?',
      answer: 'Algunos medios usan flujos de audio y video separados. Usa el modo de grabación o combina las pistas con herramientas de terceros.'
    },
    {
      question: '¿Dónde se almacenan mis archivos descargados?',
      answer: 'Los archivos se guardan en la carpeta de descarga predeterminada de tu navegador. Si no los encuentras, revisa tu historial de descargas o configuración del navegador.'
    }
  ]
}

export async function generateMetadata({ params }: { params: { locale: Locale } }): Promise<Metadata> {
  const metadata = METADATA[params.locale]
  if (!metadata) {
    notFound()
  }

  return genPageMetadata({
    title: metadata.title,
    description: metadata.description,
    pathname: '/downloader',
    locale: params.locale,
  })
}

export default function DownloaderPage({ params }: { params: { locale: Locale } }) {
  const t = useTranslations('downloader')
  const messages = useMessages()
  const faqs = FAQs[params.locale] || FAQs.en

  return (
    <main className="mb-8">
      {/* Hero Section with existing background */}
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <div className="flex flex-col gap-4 items-center text-center">
              <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">
                {t('title')}
              </h1>
              <h2 className="text-gray-500 text-lg lg:text-xl">
                {t('subtitle')}
              </h2>
            </div>

            {/* Download Manager Component */}
            <NextIntlClientProvider messages={pick(messages, ['downloader', 'downloadComponents'])}>
              <DownloadManager />
            </NextIntlClientProvider>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="self-stretch inline-flex justify-start items-start gap-8">
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Video className="w-8 h-8 text-primary-700" fill="currentColor" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature1Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature1Desc')}</div>
              </div>
            </div>
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Download className="w-8 h-8 text-primary-700" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature2Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature2Desc')}</div>
              </div>
            </div>
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Zap className="w-8 h-8 text-primary-700" fill="currentColor" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature3Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature3Desc')}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <section className="px-4 py-24 bg-gray-50 flex flex-col justify-start items-center overflow-hidden">
        <div className="w-full max-w-[1280px] flex flex-col lg:flex-row justify-start items-start lg:items-center gap-8 lg:gap-16">
          {/* Image Section - appears first on mobile, second on desktop */}
          <div className="flex-1 rounded-lg flex justify-center items-center lg:order-2">
            <div className="w-full max-w-[608px] rounded-lg">
              <img className="w-full h-[512px] object-cover rounded-lg" src="/images/how-to-use-demo.png" alt="How to use SnapAny demonstration" />
            </div>
          </div>
          {/* Text Section - appears second on mobile, first on desktop */}
          <div className="flex-1 inline-flex flex-col justify-start items-start gap-8 lg:order-1">
            <div className="self-stretch flex flex-col justify-center items-start gap-4">
              <div className="self-stretch justify-start text-gray-900 text-3xl font-extrabold font-['Inter'] leading-9">{t('howToUseTitle')}</div>
            </div>
            <div className="self-stretch flex flex-col justify-start items-start gap-8">
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center mt-1">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">1</div>
                </div>
                <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step1Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step1Desc')}</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center mt-1">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">2</div>
                </div>
                <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step2Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step2Desc')}</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center mt-1">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">3</div>
                </div>
                <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step3Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step3Desc')}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4">{t('faq')}</h2>
          <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
            {t.rich('faqDesc', {
              discord: (chunks) => (
                <a
                  href={siteConfig.socials.discord}
                  target="_blank"
                  rel="nofollow"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {chunks}
                </a>
              ),
              telegram: (chunks) => (
                <a
                  href={siteConfig.socials.telegram}
                  target="_blank"
                  rel="nofollow"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {chunks}
                </a>
              ),
            })}
          </p>
          <div className="max-w-5xl mx-auto">
            <Accordion flush collapseAll={false}>
              {faqs.map((item, index) => (
                <AccordionPanel key={index}>
                  <AccordionTitle className="bg-transparent dark:bg-transparent">{item.question}</AccordionTitle>
                  <AccordionContent>{item.answer}</AccordionContent>
                </AccordionPanel>
              ))}
            </Accordion>
          </div>
        </div>
      </section>
    </main>
  )
}