import type { Config } from 'tailwindcss'
import colors from 'tailwindcss/colors'
import flowbite from 'flowbite-react/tailwind'

const config = {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    flowbite.content(),
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        primary: colors.blue,
      },
      boxShadow: {
        'lg-light': '0 10px 15px -3px rgba(255, 255, 255, 0.1), 0 4px 6px -2px rgba(255, 255, 255, 0.05)',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography'), flowbite.plugin()],
} satisfies Config

export default config
