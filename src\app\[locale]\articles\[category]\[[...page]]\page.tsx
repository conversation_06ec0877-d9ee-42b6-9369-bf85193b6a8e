import { notFound } from 'next/navigation'
import { Locale, siteConfig } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { MILLISECONDS_IN_SECOND } from '@gongyinshi/common/datetime'
import { ArticleMeta, PaginationInfo } from '@/types/global'
import { get, range, isEmpty } from 'lodash-es'
import { getTranslations } from 'next-intl/server'
import { Link } from '@/navigation'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'

async function getData(
  locale: Locale,
  category: string,
  page: number
): Promise<{ articles: ArticleMeta[]; pagination: PaginationInfo }> {
  const res = await fetch(
    `${process.env.CMS_API_BASE_URL}/api/articles?sort[0]=publishedAt:desc&filters[category][$eq]=${category}&fields[0]=title&fields[1]=slug&fields[2]=description&pagination[pageSize]=10&pagination[page]=${page}&locale=${locale}`,
    {
      method: 'GET',
      headers: {
        Authorization: `bearer ${process.env.CMS_API_TOKEN}`,
      },
      signal: AbortSignal.timeout(30 * MILLISECONDS_IN_SECOND),
      next: { tags: ['articles'] },
    }
  )
  const j = await res.json()
  const articles = j.data
  const pagination: PaginationInfo = j.meta.pagination
  return { articles, pagination }
}

const ArticleListMetadata: Record<string, Record<Locale, { title: string; description: string }>> = {
  tutorial: {
    en: {
      title: 'Tutorials',
      description:
        'Discover how to download and save media resources such as videos, Internet photos, GIFs, etc. to local or mobile phone albums on computers, mobile phones (iPhone, iPad, Android), and other devices. Here is a collection of tutorials suitable for novices.',
    },
    zh: {
      title: '使用教程',
      description:
        '浏览如何在电脑、手机(iPhone、iPad、Android)等设备上下载保存视频、网络照片、GIF等媒体资源到本地或手机相册。这里汇聚了一系列适合新手小白的使用教程。',
    },
    ja: {
      title: 'チュートリアル',
      description: `ビデオ、インターネットの写真、GIFなどのメディアリソースをコンピューターや携帯電話（iPhone、iPad、Android）、その他のデバイスのローカルまたは携帯電話のアルバムにダウンロードして保存する方法を発見しましょう。初心者に適したチュートリアルのコレクションです。`,
    },
    es: {
      title: 'Tutoriales',
      description: `Descubre cómo descargar y guardar recursos mediáticos como videos, fotos de Internet, GIFs, etc., en álbumes locales o del teléfono móvil en computadoras, teléfonos móviles (iPhone, iPad, Android) y otros dispositivos. Aquí encontrarás una colección de tutoriales adecuados para principiantes.`,
    },
  },
}

function getPagePathname(category: string, page?: number) {
  let pathname = `/articles/${category}`
  if (page && page > 1) {
    pathname += `/${page}`
  }
  return pathname
}

export async function generateMetadata({
  params,
}: {
  params: { locale: Locale; category: string; page: string[] }
}): Promise<Metadata> {
  const category = params.category.toLowerCase()
  const metadata = get(ArticleListMetadata, [category, params.locale])
  if (!metadata) {
    notFound()
  }
  const page = parseInt(params.page && params.page[0]) || 1

  let title = metadata.title
  if (page > 1) {
    const t = await getTranslations('global')
    title += t('page', { page: page })
  }

  return genPageMetadata({
    title: `${title} - ${siteConfig.name}`,
    description: metadata.description,
    pathname: getPagePathname(category, page),
    locale: params.locale,
  })
}

export default async function Page({ params }: { params: { locale: Locale; category: string; page: string[] } }) {
  const category = params.category.toLowerCase()
  const metadata = get(ArticleListMetadata, [category, params.locale])
  if (!metadata) {
    notFound()
  }

  const page = parseInt(params.page && params.page[0]) || 1
  const { articles, pagination } = await getData(params.locale, category, page)
  if (isEmpty(articles)) {
    notFound()
  }

  return (
    <>
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <h1 className="text-center text-3xl leading-8 font-extrabold tracking-tight text-gray-900 md:text-4xl py-12 sm:py-16">
            {metadata.title}
          </h1>
        </div>
      </section>
      <main className="container max-w-4xl pb-12 sm:pb-16">
        <div className="flex flex-col divide-y divide-slate-200">
          {articles.map((article, index) => (
            <div key={index} className="py-4">
              <h2 className="text-lg font-bold pb-2">
                <Link href={`/article/${article.slug}`} className="hover:underline">
                  {article.title}
                </Link>
              </h2>
              <p className="text-gray-600">{article.description}</p>
            </div>
          ))}
        </div>

        <Pagination className="flex justify-end mt-4">
          <PaginationContent>
            {pagination.page !== 1 && (
              <PaginationItem>
                <PaginationPrevious href={getPagePathname(category, pagination.page - 1)} prefetch={false} />
              </PaginationItem>
            )}
            {range(1, pagination.pageCount + 1).map((index) => (
              <PaginationItem key={index}>
                <PaginationLink
                  href={getPagePathname(category, index)}
                  isActive={index === pagination.page}
                  prefetch={false}
                >
                  {index}
                </PaginationLink>
              </PaginationItem>
            ))}
            {pagination.page !== pagination.pageCount && (
              <PaginationItem>
                <PaginationNext href={getPagePathname(category, pagination.page + 1)} prefetch={false} />
              </PaginationItem>
            )}
          </PaginationContent>
        </Pagination>
      </main>
    </>
  )
}
