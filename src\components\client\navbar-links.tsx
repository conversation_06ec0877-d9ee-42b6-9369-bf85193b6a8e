'use client'

import * as React from 'react'
import { Link } from '@/navigation'
import { Locale } from '@/config/global'
import { SiteID, sites } from '@/config/downloader'
import { i18nConfig } from '@/config/global'
import { useParams } from 'next/navigation'
import { NavbarLink } from 'flowbite-react'

export function NavbarLinks() {
  const params = useParams<{ locale: Locale; site: SiteID }>()
  // move current site to the front
  // const currentSite = sites.find(site => site.id === params.site)
  // const sites_ = sites.filter(site => site.id !== params.site)
  // if (currentSite) {
  //   sites_.unshift(currentSite)
  // }

  return (
    <>
      {sites.map((site) => (
        <NavbarLink as={Link} prefetch={false} key={site.id} href={`/${site.id}`} active={params.site == site.id}>
          {site.localeName[params.locale] ?? site.localeName[i18nConfig.defaultLocale]}
        </NavbarLink>
      ))}
    </>
  )
}
