import type { Metadata } from 'next'
import { Locale, siteConfig } from '@/config/global'
import { SiteFooter } from '@/components/site-footer'
import { GoogleAnalytics } from '@next/third-parties/google'
import { SiteHeader } from '@/components/site-header'
import { CustomTheme } from '@/components/custom-theme'

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: siteConfig.name,
  other: {
    // 告知多内核浏览器 优先通过WebKit内核渲染
    renderer: 'webkit',
    'force-rendering': 'webkit',
    'google-adsense-account': 'ca-pub-****************',
  },
}

export default function LocaleLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode
  params: { locale: Locale }
}>) {
  return (
    <html lang={locale}>
      <body>
        <CustomTheme>
          <SiteHeader />
          {children}
          <SiteFooter />
        </CustomTheme>
        <GoogleAnalytics gaId="G-8BFPWBG3X1" />
      </body>
    </html>
  )
}
